<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.xjzs.mapper.ProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="projectResultMap" type="org.springblade.modules.xjzs.pojo.entity.ProjectEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="content" property="content"/>
        <result column="budget" property="budget"/>
        <result column="procurement_method" property="procurementMethod"/>
        <result column="dept" property="dept"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="algorithm_category" property="algorithmCategory"/>
        <result column="project_status" property="projectStatus"/>
        <result column="handler_id" property="handlerId"/>
    </resultMap>
    <!-- 已完成项目查询映射结果 -->
    <resultMap id="projectVOResultMap" type="org.springblade.modules.xjzs.pojo.vo.ProjectVO"></resultMap>

    <select id="selectProjectPage" resultMap="projectVOResultMap">
        select
            t0.*,
            t1.total_price as max_price_limit,
            t1.report_id as report_code,
            t1.id as report_id,
            t2.real_name as handler_user_name,
            t3.dict_value as project_status_name,
            (case when t4.id is not null then 1 else 0 end) as is_supplier_inquiry
        from
            xjzs_project t0
        left join xjzs_project_report t1 on t1.project_id = t0.id and t1.is_deleted = 0
        left join blade_user t2 on t0.handler_id = t2.id
        left join (select * from blade_dict_biz where is_deleted = 0 and code = 'project_status' and dict_key != '-1') t3 on t0.project_status::TEXT = t3.dict_key
        left join (select * from xjzs_project_inquiry_record where is_deleted = 0) t4 on t0.id = t4.project_id
        where t0.is_deleted = 0
        <if test="project.id != null and project.id != ''">
            AND t0.id = #{project.id}
        </if>
        <if test="project.name != null and project.name != ''">
            AND t0.name LIKE CONCAT('%', #{project.name}, '%')
        </if>
        <if test="project.handlerId != null and project.handlerId != ''">
            AND t0.handler_id = #{project.handlerId}
        </if>
        <if test="project.type != null and project.type != ''">
            AND t0.type = #{project.type}
        </if>
        <if test="project.dept != null and project.dept != ''">
            AND t0.dept = #{project.dept}
        </if>
        <if test="project.projectStatus != null and project.projectStatus != ''">
            AND t0.project_status = #{project.projectStatus}
        </if>
        order by (CASE WHEN t0.project_status = 0 THEN 100 ELSE t0.project_status END), create_time DESC
    </select>

    <select id="exportProject" resultType="org.springblade.modules.xjzs.excel.ProjectExcel">
        SELECT * FROM xjzs_project ${ew.customSqlSegment}
    </select>

</mapper>
