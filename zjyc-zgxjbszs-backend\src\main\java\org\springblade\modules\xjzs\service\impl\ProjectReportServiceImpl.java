package org.springblade.modules.xjzs.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.modules.dify.resp.BlockResponse;
import org.springblade.modules.dify.resp.WorkflowStreamResponse;
import org.springblade.modules.dify.service.DifyService;
import org.springblade.modules.xjzs.entity.ProjectReportEntity;
import org.springblade.modules.xjzs.mapper.ProjectReportMapper;
import org.springblade.modules.xjzs.pojo.entity.ProjectEntity;
import org.springblade.modules.xjzs.pojo.vo.ProjectReportCheckResultVO;
import org.springblade.modules.xjzs.service.IProjectReportService;
import org.springblade.modules.xjzs.service.IProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目报告服务实现类
 */
@Slf4j
@Service
public class ProjectReportServiceImpl extends BaseServiceImpl<ProjectReportMapper, ProjectReportEntity> implements IProjectReportService {

    @Autowired
    private IProjectService projectService;

    @Autowired
    private PriceCalculationService priceCalculationService;

    @Autowired
    private DifyService difyService;

    @Value("${dify.key.compliance}")
    private String complianceApiKey;

    @Value("${dify.key.engineeringCompliance:app-engineering-compliance-default}")
    private String engineeringComplianceApiKey;

    @Value("${dify.key.procurementCompliance:app-procurement-compliance-default}")
    private String procurementComplianceApiKey;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveProjectReport(ProjectReportEntity report) {
        boolean status = false;
        if(checkReportExists(report.getId())) {
            status = this.updateById(report);
        } else {
            status = this.save(report);
        }

        // 项目状态设为2：已完成
        LambdaUpdateWrapper<ProjectEntity> projectUpdateWrapper = new LambdaUpdateWrapper<>();
        projectUpdateWrapper.eq(ProjectEntity::getId, report.getProjectId())
                .set(ProjectEntity::getProjectStatus, 2);
        projectService.update(projectUpdateWrapper);

        return status;
    }

    @Override
    public boolean checkReportExists(Long projectId) {
        Integer count = baseMapper.checkReportExists(projectId);
        return count != null && count > 0;
    }

    @Override
    public void updateReportCheckContent(Long projectId,  String checkReportContent) {
        ProjectReportEntity report = getProjectReport(projectId);
        if(report!=null){
            report.setCheckReportContent(checkReportContent);
            this.saveOrUpdate(report);
        }
    }



    @Override
    public List<ProjectReportCheckResultVO> checkReport(Long projectId) {

        ProjectReportCheckResultVO vo=new ProjectReportCheckResultVO();
        try {

           //通过projectId获取报告信息
            ProjectReportEntity report = getProjectReport(projectId);
            // 调用Dify API进行规格分析
            BlockResponse response = difyService.blockingMessage(
                    report.getReportContent(),
                    0L,
                    complianceApiKey,
                    null
            );

            if (response != null && StringUtil.isNotBlank(response.getAnswer())) {
                String jsonStr = response.getAnswer();

                // 移除思考标签
                if (jsonStr.contains("</think>")) {
                    int thinkStart = jsonStr.indexOf("</think>");
                    if (thinkStart >= 0) {
                        int startIndex = jsonStr.indexOf("{", thinkStart);
                        int endIndex = jsonStr.lastIndexOf("}") + 1;
                        if (startIndex >= 0 && endIndex > startIndex) {
                            jsonStr = jsonStr.substring(startIndex, endIndex);
                        }
                    }
                } else if (!jsonStr.startsWith("{")) {
                    int startIndex = jsonStr.indexOf("{");
                    int endIndex = jsonStr.lastIndexOf("}") + 1;
                    if (startIndex >= 0 && endIndex > startIndex) {
                        jsonStr = jsonStr.substring(startIndex, endIndex);
                    }
                }

                JSONObject jsonObject = JSON.parseObject(jsonStr);
                vo.setResult( jsonObject.getString("result"));
                vo.setDetail( jsonObject.getString("detail"));
                vo.setType( "符合政府采购相关法规");
                // 保存到数据库



            } else {
                log.trace("培训合规校验 Dify API返回结果为空或无效");
            }

        } catch (Exception e) {
            log.error("调用Dify API失败，批次: {}", e);
        }

        //预留后续多种校验
        List<ProjectReportCheckResultVO> list=new ArrayList<>();
        list.add(vo);
        return list;
    }

    @Override
    public ProjectReportEntity getProjectReport(Long projectId) {
        LambdaQueryWrapper<ProjectReportEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectReportEntity::getProjectId, projectId)
                   .eq(ProjectReportEntity::getIsDeleted, 0)
                   .orderByDesc(ProjectReportEntity::getCreateTime)
                   .last("LIMIT 1");
        
        ProjectReportEntity report = this.getOne(queryWrapper);
        
        // 如果找到报告，关联查询项目信息
        if (report != null) {
            ProjectEntity project = projectService.getById(report.getProjectId());
            report.setProject(project);
        }
        
        return report;
    }


    /**
     * 清理报告内容，移除计算结果中的思考过程
     * @param reportContent 原始报告内容
     * @return 清理后的报告内容
     */
    private String cleanReportContent(String reportContent) {
        if (StringUtil.isBlank(reportContent)) {
            return reportContent;
        }

        try {
            log.info("开始清理报告内容，原始长度: {}", reportContent.length());

            // 解析报告内容JSON
            JSONObject reportJson = JSON.parseObject(reportContent);

            // 检查是否有formData字段
            if (reportJson.containsKey("formData")) {
                JSONObject formData = reportJson.getJSONObject("formData");

                // 检查是否有calculationResult字段
                if (formData.containsKey("calculationResult")) {
                    JSONObject calculationResult = formData.getJSONObject("calculationResult");

                    // 移除calculationProcess字段（包含思考过程）
                    if (calculationResult.containsKey("calculationProcess")) {
                        Object calculationProcess = calculationResult.get("calculationProcess");
                        calculationResult.remove("calculationProcess");
                        log.info("已移除报告内容中的计算思考过程，思考内容长度: {}",
                                calculationProcess != null ? calculationProcess.toString().length() : 0);
                    }
                }
            }

            String cleanedContent = reportJson.toJSONString();
            log.info("报告内容清理完成，清理后长度: {}", cleanedContent.length());

            return cleanedContent;

        } catch (Exception e) {
            log.warn("清理报告内容失败，使用原始内容: {}", e.getMessage());
            return reportContent;
        }
    }

    @Override
    public Flux<WorkflowStreamResponse> checkComplianceWithDifyStream(Long projectId) {
        return Flux.defer(() -> {
            try {
                // 通过projectId获取报告信息
                ProjectReportEntity report = getProjectReport(projectId);
                if (report == null) {
                    WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                    errorResponse.setEvent("error");
                    errorResponse.setErrorInfo("未找到项目报告");
                    return Flux.just(errorResponse);
                }

                // 构建工作流请求体
                Map<String, Object> requestBody = new HashMap<>();
                Map<String, String> inputs = new HashMap<>();

                // 清理报告内容，移除思考过程
                String cleanedReportContent = cleanReportContent(report.getReportContent());
                inputs.put("report", cleanedReportContent);

                requestBody.put("inputs", inputs);
                requestBody.put("response_mode", "streaming");
                requestBody.put("user", "compliance-check");

                log.info("开始调用Dify工作流进行合规性检查，项目ID: {}", projectId);

                // 直接返回Dify工作流的流式响应，不做任何处理
                return difyService.streamingMessageWithRequestBodyWorkflow(
                        requestBody,
                        complianceApiKey
                );

            } catch (Exception e) {
                log.error("调用Dify工作流失败", e);
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    @Override
    public Flux<WorkflowStreamResponse> checkEngineeringComplianceWithDifyStream(Long projectId) {
        return Flux.defer(() -> {
            try {
                // 通过projectId获取报告信息
                ProjectReportEntity report = getProjectReport(projectId);
                if (report == null) {
                    WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                    errorResponse.setEvent("error");
                    errorResponse.setErrorInfo("未找到项目报告");
                    return Flux.just(errorResponse);
                }

                // 构建工作流请求体
                Map<String, Object> requestBody = new HashMap<>();
                Map<String, String> inputs = new HashMap<>();

                // 清理报告内容，移除思考过程
                String cleanedReportContent = cleanReportContent(report.getReportContent());
                inputs.put("report", cleanedReportContent);

                // 添加工程咨询服务类型参数
                String consultingType = extractConsultingTypeFromReport(cleanedReportContent);
                inputs.put("ConsultingType", consultingType);

                log.info("工程类合规检查参数 - 项目ID: {}, 咨询服务类型: {}", projectId, consultingType);

                requestBody.put("inputs", inputs);
                requestBody.put("response_mode", "streaming");
                requestBody.put("user", "engineering-compliance-check");

                log.info("开始调用Dify工作流进行工程类合规性检查，项目ID: {}", projectId);

                // 直接返回Dify工作流的流式响应，使用工程类专用API key
                return difyService.streamingMessageWithRequestBodyWorkflow(
                        requestBody,
                        engineeringComplianceApiKey
                );

            } catch (Exception e) {
                log.error("调用Dify工作流进行工程类合规性检查失败", e);
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("工程类合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 从报告内容中提取工程咨询服务类型
     * @param reportContent 报告内容
     * @return 咨询服务类型
     */
    private String extractConsultingTypeFromReport(String reportContent) {
        if (StringUtil.isBlank(reportContent)) {
            return ""; // 默认值
        }

        try {
            // 解析JSON格式的报告内容
            JSONObject reportJson = JSON.parseObject(reportContent);

            // 首先尝试从formData.engineeringTableRows[0].category获取值
            if (reportJson.containsKey("formData")) {
                JSONObject formData = reportJson.getJSONObject("formData");
                if (formData != null && formData.containsKey("engineeringTableRows")) {
                    Object engineeringData = formData.get("engineeringTableRows");
                    if (engineeringData instanceof List) {
                        List<?> tableRows = (List<?>) engineeringData;
                        if (!tableRows.isEmpty() && tableRows.get(0) instanceof Map) {
                            Map<?, ?> firstRow = (Map<?, ?>) tableRows.get(0);

                            // 直接获取category字段的值
                            Object category = firstRow.get("category");
                            if (category != null) {
                                String consultingType = String.valueOf(category);
                                log.info("从formData.engineeringTableRows[0].category中获取到咨询类型: {}", consultingType);
                                return consultingType;
                            }
                        }
                    }
                }
            }

            // 如果在formData中没找到，尝试直接从engineeringTableRows获取
            if (reportJson.containsKey("engineeringTableRows")) {
                Object engineeringData = reportJson.get("engineeringTableRows");
                if (engineeringData instanceof List) {
                    List<?> tableRows = (List<?>) engineeringData;
                    if (!tableRows.isEmpty() && tableRows.get(0) instanceof Map) {
                        Map<?, ?> firstRow = (Map<?, ?>) tableRows.get(0);

                        // 直接获取category字段的值
                        Object category = firstRow.get("category");
                        if (category != null) {
                            String consultingType = String.valueOf(category);
                            log.info("从engineeringTableRows[0].category中获取到咨询类型: {}", consultingType);
                            return consultingType;
                        }
                    }
                }
            }

            // 如果没有找到，使用默认值
            log.info("未找到category字段，使用默认值: 竣工决算审计");
            return "竣工决算审计";

        } catch (Exception e) {
            log.warn("提取工程咨询服务类型失败，使用默认值: {}", e.getMessage());
            return "竣工决算审计";
        }
    }

    @Override
    public Flux<WorkflowStreamResponse> checkProcurementComplianceWithDifyStream(Long projectId) {
        return Flux.defer(() -> {
            try {
                // 通过projectId获取报告信息
                ProjectReportEntity report = getProjectReport(projectId);
                if (report == null) {
                    WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                    errorResponse.setEvent("error");
                    errorResponse.setErrorInfo("未找到项目报告");
                    return Flux.just(errorResponse);
                }

                // 构建工作流请求体
                Map<String, Object> requestBody = new HashMap<>();
                Map<String, String> inputs = new HashMap<>();

                // 清理报告内容，移除思考过程
                String cleanedReportContent = cleanReportContent(report.getReportContent());

                // 添加采购物品信息参数
                String procurementInfo = extractProcurementTypeFromReport(cleanedReportContent);
                inputs.put("goods_data", procurementInfo);

                log.info("采购类合规检查参数 - 项目ID: {}, 采购物品信息: {}", projectId, procurementInfo);

                requestBody.put("inputs", inputs);
                requestBody.put("response_mode", "streaming");
                requestBody.put("user", "procurement-compliance-check");

                log.info("开始调用Dify工作流进行采购类合规性检查，项目ID: {}", projectId);

                // 直接返回Dify工作流的流式响应，使用采购类专用API key
                return difyService.streamingMessageWithRequestBodyWorkflow(
                        requestBody,
                        procurementComplianceApiKey
                );

            } catch (Exception e) {
                log.error("调用Dify工作流进行采购类合规性检查失败", e);
                WorkflowStreamResponse errorResponse = new WorkflowStreamResponse();
                errorResponse.setEvent("error");
                errorResponse.setErrorInfo("采购类合规性检查失败：" + e.getMessage());
                return Flux.just(errorResponse);
            }
        });
    }

    /**
     * 从报告内容中提取采购物品信息
     * @param reportContent 报告内容
     * @return 格式化的采购物品信息字符串
     */
    private String extractProcurementTypeFromReport(String reportContent) {
        if (StringUtil.isBlank(reportContent)) {
            return ""; 
        }

        try {
            // 解析JSON格式的报告内容
            JSONObject reportJson = JSON.parseObject(reportContent);

            // 首先尝试从formData.standardTableRows获取采购物品信息
            if (reportJson.containsKey("formData")) {
                JSONObject formData = reportJson.getJSONObject("formData");
                if (formData != null && formData.containsKey("standardTableRows")) {
                    Object standardTableRowsObj = formData.get("standardTableRows");
                    if (standardTableRowsObj != null) {
                        List<Map<String, Object>> tableRows = null;
                        if (standardTableRowsObj instanceof List) {
                            tableRows = (List<Map<String, Object>>) standardTableRowsObj;
                        } else if (standardTableRowsObj instanceof String) {
                            tableRows = JSON.parseArray((String) standardTableRowsObj, (Type) Map.class);
                        }

                        if (tableRows != null && !tableRows.isEmpty()) {
                            List<Map<String, Object>> result = new ArrayList<>();

                            for (Map<String, Object> row : tableRows) {
                                if (row == null) continue;

                                Map<String, Object> item = new HashMap<>();
                                item.put("feeName", row.get("feeName"));
                                item.put("unit", row.get("unit"));

                                // 处理specification，分割为数组
                                String specification = String.valueOf(row.get("specification"));
                                List<String> attributes = new ArrayList<>();

                                if (StringUtil.isNotBlank(specification) && !"null".equals(specification)) {
                                    String[] parts = specification.split("[,，;；、\\s]+");
                                    for (String part : parts) {
                                        String trimmed = part.trim();
                                        if (StringUtil.isNotBlank(trimmed)) {
                                            attributes.add(trimmed);
                                        }
                                    }
                                }

                                if (attributes.isEmpty()) {
                                    attributes.add("无");
                                }

                                item.put("attributes", attributes);
                                result.add(item);
                            }

                            String resultStr = result.toString();
                            log.info("从formData.standardTableRows中提取到采购物品信息: {}", resultStr);
                            return resultStr;
                        }
                    }
                }
                
            }

            // 如果没有找到，使用默认值
            log.info("未找到采购物品信息，使用默认值: 货物类");
            return "";

        } catch (Exception e) {
            log.warn("提取采购类型信息失败，使用默认值: {}", e.getMessage());
            return "";
        }
    }
    }


