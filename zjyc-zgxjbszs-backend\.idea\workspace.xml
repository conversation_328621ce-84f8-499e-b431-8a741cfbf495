<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="dd62e559-0bca-4d75-b3d9-1932387de7f3" name="Changes" comment="bug fix">
      <change beforePath="$PROJECT_DIR$/src/main/java/org/springblade/modules/xjzs/controller/ProjectReportController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/org/springblade/modules/xjzs/controller/ProjectReportController.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.5.2" />
        <option name="localRepository" value="D:\maven_repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.5.2\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2yJ7Jf353ExFazSGmsZNkaFOd39" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JUnit.QuotationParsingTest.testExtractQuotationInfo.executor&quot;: &quot;Run&quot;,
    &quot;Maven.BladeX-Boot [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.BladeX-Boot [install].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.Application.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/yueshu/zhihui-conference-system/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\yueshu\湛江-最高限价助手\zjyc-zgxjbszs-backend\src\main\resources\doctemplete" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.Application">
    <configuration name="QuotationParsingTest.testExtractQuotationInfo" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="BladeX-Boot" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.springblade.modules.xjzs.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="org.springblade.modules.xjzs.controller" />
      <option name="MAIN_CLASS_NAME" value="org.springblade.modules.xjzs.controller.QuotationParsingTest" />
      <option name="METHOD_NAME" value="testExtractQuotationInfo" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="BladeX-Boot" />
      <option name="SHORTEN_COMMAND_LINE" value="MANIFEST" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="org.springblade.Application" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <option name="SHORTEN_COMMAND_LINE" value="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.QuotationParsingTest.testExtractQuotationInfo" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9f38398b9061-39b83d9b5494-intellij.indexing.shared.core-IU-241.18034.62" />
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-IU-241.18034.62" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="dd62e559-0bca-4d75-b3d9-1932387de7f3" name="Changes" comment="" />
      <created>1749540751872</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749540751872</updated>
      <workItem from="1749540752981" duration="794000" />
      <workItem from="1749541559864" duration="5443000" />
      <workItem from="1749605148122" duration="9072000" />
      <workItem from="1749692018713" duration="14766000" />
      <workItem from="1749777422117" duration="14373000" />
      <workItem from="1750036682597" duration="1298000" />
      <workItem from="1750122848777" duration="629000" />
      <workItem from="1750641774032" duration="12970000" />
      <workItem from="1750728013180" duration="8153000" />
      <workItem from="1750814551169" duration="10348000" />
      <workItem from="1750900582982" duration="3483000" />
      <workItem from="1750989767648" duration="8278000" />
      <workItem from="1751245263150" duration="66000" />
      <workItem from="1751264564252" duration="9115000" />
      <workItem from="1751334253003" duration="1474000" />
      <workItem from="1751419490826" duration="6661000" />
      <workItem from="1751438179264" duration="1407000" />
      <workItem from="1751445777670" duration="3391000" />
      <workItem from="1751504533370" duration="10622000" />
      <workItem from="1751591291893" duration="7231000" />
      <workItem from="1751851503298" duration="1949000" />
      <workItem from="1751863739668" duration="15038000" />
      <workItem from="1751937672910" duration="21344000" />
      <workItem from="1752023923504" duration="4176000" />
      <workItem from="1752030470043" duration="18000" />
      <workItem from="1752044561701" duration="8353000" />
      <workItem from="1752110409019" duration="1597000" />
      <workItem from="1752114105389" duration="13839000" />
      <workItem from="1752196504530" duration="9399000" />
      <workItem from="1752390431595" duration="3371000" />
      <workItem from="1752455750392" duration="21407000" />
      <workItem from="1752542844048" duration="12027000" />
      <workItem from="1752629892971" duration="8492000" />
      <workItem from="1752713971695" duration="26679000" />
      <workItem from="1752819164166" duration="5336000" />
      <workItem from="1753061515178" duration="11169000" />
      <workItem from="1753101550792" duration="1768000" />
      <workItem from="1753147627778" duration="8720000" />
      <workItem from="1753234282018" duration="7708000" />
      <workItem from="1753318680831" duration="9502000" />
      <workItem from="1753406447181" duration="7312000" />
      <workItem from="1753664748098" duration="10447000" />
      <workItem from="1753755502839" duration="222000" />
      <workItem from="1755138780182" duration="9716000" />
      <workItem from="1755740235023" duration="18566000" />
    </task>
    <task id="LOCAL-00001" summary="报价回函提交、相关字段添加">
      <option name="closed" value="true" />
      <created>1749799799147</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749799799147</updated>
    </task>
    <task id="LOCAL-00002" summary="询价详情接口调整">
      <option name="closed" value="true" />
      <created>1749807031655</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749807031655</updated>
    </task>
    <task id="LOCAL-00003" summary="计价规则表">
      <option name="closed" value="true" />
      <created>1750836939902</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750836939902</updated>
    </task>
    <task id="LOCAL-00004" summary="合规性检查agent接口添加">
      <option name="closed" value="true" />
      <created>1752486015802</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752486015803</updated>
    </task>
    <task id="LOCAL-00005" summary="命名调整">
      <option name="closed" value="true" />
      <created>1752488278129</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752488278129</updated>
    </task>
    <task id="LOCAL-00006" summary="bug fix">
      <option name="closed" value="true" />
      <created>1752497569298</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752497569298</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="报价回函提交、相关字段添加" />
    <MESSAGE value="询价详情接口调整" />
    <MESSAGE value="计价规则表" />
    <MESSAGE value="合规性检查agent接口添加" />
    <MESSAGE value="命名调整" />
    <MESSAGE value="bug fix" />
    <option name="LAST_COMMIT_MESSAGE" value="bug fix" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/org/springblade/modules/xjzs/controller/AuditController.java</url>
          <line>63</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>